import type {
	GetUserRes,
	OnboardUserRes,
	UpdateUserRes,
	ProfilePictureUploadUrlRes,
	ProfilePictureUrlRes,
	GetAnalyticsRes,
} from "@/features/user/types";
import { api } from "@/lib/api";
import { OnboardUserValidation } from "@/features/auth/schema";

export const getUser = (id: string) => {
	return api.get<GetUserRes>(`user/get/${id}`);
};

export const onboardUser = (data: OnboardUserValidation) => {
	return api.post<OnboardUserRes>("user/onboardUser", data);
};

export const updateUser = (data: Partial<OnboardUserValidation>) => {
	return api.patch<UpdateUserRes>("user/update", data);
};

export const getProfilePictureUploadUrl = () => {
	return api.get<ProfilePictureUploadUrlRes>("user/getProfilePictureUploadUrl");
};

export const getProfilePictureUrl = (userId: string) => {
	return api.get<ProfilePictureUrlRes>(`user/getProfilePictureUrl/${userId}`);
};

export const getAnalytics = () => {
	return api.get<GetAnalyticsRes>("user/analytics");
};

export const updateAnalytics = () => {
	return api.patch<GetAnalyticsRes>("user/updateAnalytics");
};
