import { Test } from "@/features/mcqs/types";
import TestHeader from "./test-header";
import SubjectTabs from "./subject-tabs";

type TestLayoutProps = {
	test: Test;
	liveCheckEnabled: boolean;
	setLiveCheckEnabled: (enabled: boolean) => void;
	selectedAnswers: Record<string, number>;
	answerStatus: Record<string, boolean>;
	showResults: boolean;
	testTimesUp: boolean;
	onAnswerSelect: (questionId: string, optionIndex: number) => void;
	onCheckAnswers: () => void;
	setTestTimesUp: (enabled: boolean) => void;
};

const TestLayout = ({
	test,
	liveCheckEnabled,
	setLiveCheckEnabled,
	selectedAnswers,
	answerStatus,
	showResults,
	onAnswerSelect,
	onCheckAnswers,
	testTimesUp,
	setTestTimesUp,
}: TestLayoutProps) => {
	return (
		<div className="flex-1 h-screen">
			<TestHeader
				test={test}
				liveCheckEnabled={liveCheckEnabled}
				setLiveCheckEnabled={setLiveCheckEnabled}
				setTestTimesUp={setTestTimesUp}
				quizSubmitted={showResults}
			/>
			<div className="flex-1">
				<SubjectTabs
					test={test}
					liveCheckEnabled={liveCheckEnabled}
					selectedAnswers={selectedAnswers}
					answerStatus={answerStatus}
					showResults={showResults}
					onAnswerSelect={onAnswerSelect}
					onCheckAnswers={onCheckAnswers}
					testTimesUp={testTimesUp}
				/>
			</div>
		</div>
	);
};

export default TestLayout;
