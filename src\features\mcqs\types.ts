import { subjects } from "../learning/constants";

export type MCQ = {
	id: string;
	question: string;
	options: string[];
	correctAnswer: number;
	subject: (typeof subjects)[number]["id"];
	tag?: string;
	difficulty?: "easy" | "medium" | "hard";
	description?: string;
};

export type Test = {
	id: string;
	title: string;
	totalQuestions: number;
	duration: string;
	liveCheck: boolean;
	mcqs: MCQ[];
};
