import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import { useMediaQuery } from 'react-responsive';
import { createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/charts";
import { useGetAnalytics } from "@/lib/queries/user.query";
import { AnalyticsEmptyState } from "@/components/common";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Star } from "react-feather";

// Types
interface AnalyticsData {
	name: string;
	total: number;
	correct: { value: number; fill: string };
	wrong: { value: number; fill: string };
}

interface TimeFrameData {
	mcqsSolvedCount: number;
	correctAnswers: number;
	wrongAnswers: number;
	mcqsType: Record<string, number>;
	difficultyType: Record<string, number>;
	subjects: Record<string, number>;
	subject_division: Record<string, any>;
}

type TimeFrame = 'twentyFourHours' | 'sevenDays' | 'thirtyDays' | 'overall';

// Constants
const CHART_COLORS = {
	correct: "rgba(89, 54, 205, 1)",
	wrong: "rgba(89, 54, 205, 0.5)",
} as const;

const TIME_FRAME_OPTIONS = [
	{ key: 'twentyFourHours' as const, label: 'Last 24h' },
	{ key: 'sevenDays' as const, label: 'Last 7 days' },
	{ key: 'overall' as const, label: 'Overall Performance' },
] as const;

// Utility functions
const formatName = (name: string): string => {
	return name === name.toUpperCase()
		? name
		: name.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
};

const calculateCorrectWrongFromSubjects = (
	subjectDivision: Record<string, any>,
	type: string,
	category: 'type' | 'difficulty'
): { correct: number; wrong: number } => {
	let correct = 0;
	let wrong = 0;

	Object.values(subjectDivision || {}).forEach((subjectData: any) => {
		const categoryData = subjectData?.[category]?.[type];
		if (categoryData) {
			correct += categoryData.correct || 0;
			wrong += categoryData.wrong || 0;
		}
	});

	return { correct, wrong };
};

const transformDataToAnalytics = (
	data: Record<string, number>,
	subjectDivision: Record<string, any>,
	category: 'type' | 'difficulty'
): AnalyticsData[] => {
	return Object.entries(data || {}).map(([key, value]) => {
		const { correct, wrong } = calculateCorrectWrongFromSubjects(subjectDivision, key, category);
		return {
			name: key,
			total: value,
			correct: { value: correct, fill: CHART_COLORS.correct },
			wrong: { value: wrong, fill: CHART_COLORS.wrong },
		};
	});
};

// Analytics-specific loading components
const LoadingBlock = ({ className = "" }: { className?: string }) => (
	<div className={`animate-pulse bg-slate-100 rounded-md ${className}`}></div>
);

// Reusable Components
const AnalyticsCard = ({
	title,
	value,
	subtitle,
	children,
	className = "",
	showTotal = false,
	onShowTotal
}: {
	title: string;
	value: string | number;
	subtitle: string;
	children: React.ReactNode;
	className?: string;
	showTotal?: boolean;
	onShowTotal?: () => void;
}) => (
	<div className={`mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 ${className}`}>
		<div className="mb-6">
			<h3 className="text-sm font-bold text-gray-400 mb-2.5">{title}</h3>
			<p className="text-[32px] font-medium text-gray-700">
				{value} <span className="text-base font-medium text-gray-400">{subtitle}</span>
			</p>
			{showTotal && onShowTotal && (
				<button
					onClick={onShowTotal}
					className="text-accent text-sm mt-2 underline hover:no-underline transition-all duration-200"
				>
					Show Total
				</button>
			)}
		</div>
		{children}
	</div>
);

const AnalyticsSection = ({
	title,
	data,
	borderColor = "border-gray-200",
	onItemClick
}: {
	title: string;
	data: AnalyticsData[];
	borderColor?: string;
	onItemClick?: (item: AnalyticsData) => void;
}) => (
	<div className="flex flex-col justify-between">
		<h3 className="text-sm font-semibold text-gray-600 mb-4">{title}</h3>
		<div className="space-y-2.5">
			{data.map((item, index) => (
				<div
					key={index}
					onClick={() => onItemClick?.(item)}
					className={`flex items-center justify-between py-2.5 px-5 bg-white border ${borderColor} rounded-lg shadow-sm ${
						onItemClick ? 'cursor-pointer hover:border-accent hover:shadow-md transition-all duration-200 hover:bg-purple-50' : ''
					}`}
				>
					<div className="flex flex-col">
						<span className="text-sm font-semibold text-gray-600 mb-1">
							{formatName(item.name)}
						</span>
						<div className="flex items-baseline gap-1">
							<span className="text-3xl font-normal text-black">{item.total}</span>
							<span className="text-sm font-normal text-gray-400">total</span>
						</div>
					</div>
					<div className={`flex-shrink-0 ${onItemClick ? 'hover:scale-105 transition-transform duration-200' : ''}`}>
						<DonutChart
							data={[
								{ name: "Correct", value: item.correct.value, fill: item.correct.fill },
								{ name: "Wrong", value: item.wrong.value, fill: item.wrong.fill },
							]}
							showLegend={false}
							showTooltip={true}
							innerRadius={19}
							outerRadius={31}
							className="w-[63px] h-[63px]"
						/>
					</div>
				</div>
			))}
		</div>
	</div>
);

const LoadingSection = ({ title, count, borderColor = "border-gray-200" }: {
	title: string;
	count: number;
	borderColor?: string;
}) => (
	<div className="flex flex-col justify-between">
		<LoadingBlock className={`h-4 w-${title.length > 10 ? '24' : '20'} mb-4`} />
		<div className="space-y-2.5">
			{[...Array(count)].map((_, i) => (
				<div key={i} className={`flex items-center justify-between py-2.5 px-5 bg-white border ${borderColor} rounded-lg shadow-sm`}>
					<div className="flex flex-col">
						<LoadingBlock className="h-4 w-16 mb-1" />
						<div className="flex items-baseline gap-1">
							<LoadingBlock className="h-6 w-8" />
							<LoadingBlock className="h-3 w-8" />
						</div>
					</div>
					<LoadingBlock className="w-[63px] h-[63px] rounded-full" />
				</div>
			))}
		</div>
	</div>
);

const AnalyticsMainLoading = () => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
		<div className="mb-6">
			<LoadingBlock className="h-4 w-48 mb-2.5" />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className="h-8 w-16" />
				<LoadingBlock className="h-4 w-12" />
			</div>
		</div>
		<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
			<div className="flex flex-col justify-between">
				<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
				<div className="space-y-2">
					{[...Array(2)].map((_, i) => (
						<div key={i} className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<LoadingBlock className="w-4 h-4 rounded-full" />
								<LoadingBlock className="h-4 w-24" />
							</div>
							<LoadingBlock className="h-4 w-8" />
						</div>
					))}
				</div>
			</div>
			<LoadingSection title="MCQ's Type" count={2} borderColor="border-[#D2D5DA]" />
			<LoadingSection title="Difficulty Type" count={3} />
			<LoadingSection title="Subject Wise" count={4} />
		</div>
	</div>
);

const AnalyticsFutureLoading = ({ type }: { type: 'left' | 'right' }) => (
	<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300 flex flex-col">
		<div className="mb-6">
			<LoadingBlock className={`h-4 ${type === 'left' ? 'w-32' : 'w-40'} mb-2.5`} />
			<div className="flex items-baseline gap-2">
				<LoadingBlock className={`h-8 ${type === 'left' ? 'w-16' : 'w-12'}`} />
				<LoadingBlock className={`h-4 ${type === 'left' ? 'w-16' : 'w-20'}`} />
			</div>
		</div>
		{type === 'left' ? (
			<div className="h-full flex flex-col justify-between">
				<LoadingBlock className="w-[140px] h-[140px] rounded-full mx-auto mt-8 mb-10" />
				<div className="space-y-2">
					{[...Array(2)].map((_, i) => (
						<div key={i} className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<LoadingBlock className="w-4 h-4 rounded-full" />
								<LoadingBlock className="h-4 w-24" />
							</div>
							<LoadingBlock className="h-4 w-8" />
						</div>
					))}
				</div>
			</div>
		) : (
			<div className="space-y-4">
				{[...Array(4)].map((_, i) => (
					<div key={i} className="space-y-2">
						<LoadingBlock className="h-4 w-16" />
						<LoadingBlock className="h-4 w-full" />
					</div>
				))}
			</div>
		)}
	</div>
);

// Test Detail Components
const TestDetailSection = ({
	title,
	data,
	borderColor = "border-gray-200",
	showTotal = false
}: {
	title: string;
	data: Record<string, number>;
	borderColor?: string;
	showTotal?: boolean;
}) => (
	<div className="flex flex-col justify-between">
		<div className="flex items-center justify-between mb-4">
			<h3 className="text-sm font-semibold text-gray-600">{title}</h3>
			{showTotal && (
				<button className="text-accent text-sm underline hover:no-underline transition-all duration-200">
					Show Total
				</button>
			)}
		</div>
		<div className="space-y-2.5">
			{Object.entries(data).map(([key, total], index) => (
				<div key={index} className={`flex items-center justify-between py-2.5 px-5 bg-white border ${borderColor} rounded-lg shadow-sm`}>
					<div className="flex flex-col">
						<span className="text-sm font-semibold text-gray-600 mb-1">
							{formatName(key)}
						</span>
						<div className="flex items-baseline gap-1">
							<span className="text-3xl font-normal text-black">{total}</span>
							<span className="text-sm font-normal text-gray-400">total</span>
						</div>
					</div>
					<div className="flex-shrink-0">
						<DonutChart
							data={[
								{ name: "Correct", value: Math.round(total * 0.75), fill: CHART_COLORS.correct },
								{ name: "Wrong", value: Math.round(total * 0.25), fill: CHART_COLORS.wrong },
							]}
							showLegend={false}
							showTooltip={true}
							innerRadius={19}
							outerRadius={31}
							className="w-[63px] h-[63px]"
						/>
					</div>
				</div>
			))}
		</div>
	</div>
);

const FuturePredictionComingSoon = () => (
	<div className="mb-9 px-6 py-20 bg-white rounded-3xl border border-gray-300">
		<div className="text-center">
			<div className="mb-6">
				<div className="w-16 h-16 mx-auto mb-4 bg-[rgba(89,54,205,0.1)] rounded-full flex items-center justify-center">
					<svg
						className="w-8 h-8 text-accent"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M13 10V3L4 14h7v7l9-11h-7z"
						/>
					</svg>
				</div>
				<h3 className="text-2xl font-bold text-gray-800 mb-2">Coming Soon</h3>
				<p className="text-gray-600 max-w-md mx-auto">
					We're working on advanced AI-powered predictions to help you
					understand your future performance trends and identify areas for
					improvement.
				</p>
			</div>
			<div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
				<div className="flex items-center gap-2 px-3 py-1 bg-gray-50 rounded-full">
					<div className="w-2 h-2 bg-purple-400 rounded-full"></div>
					<span>Performance Predictions</span>
				</div>
				<div className="flex items-center gap-2 px-3 py-1 bg-gray-50 rounded-full">
					<div className="w-2 h-2 bg-purple-400 rounded-full"></div>
					<span>Subject-wise Forecasting</span>
				</div>
				<div className="flex items-center gap-2 px-3 py-1 bg-gray-50 rounded-full">
					<div className="w-2 h-2 bg-purple-400 rounded-full"></div>
					<span>Improvement Recommendations</span>
				</div>
			</div>
		</div>
	</div>
);

// Subject Detail Modal Component
const SubjectDetailModal = ({
	isOpen,
	onClose,
	subjectName,
	subjectData,
}: {
	isOpen: boolean;
	onClose: () => void;
	subjectName: string;
	subjectData: any;
}) => {
	if (!subjectData) return null;

	const transformSubjectData = (data: Record<string, any>, category: string): AnalyticsData[] => {
		return Object.entries(data || {}).map(([key, value]: [string, any]) => ({
			name: key,
			total: (value.correct || 0) + (value.wrong || 0),
			correct: { value: value.correct || 0, fill: CHART_COLORS.correct },
			wrong: { value: value.wrong || 0, fill: CHART_COLORS.wrong },
		}));
	};

	const difficultyData = transformSubjectData(subjectData.difficulty, 'difficulty');
	const typeData = transformSubjectData(subjectData.type, 'type');

	const totalCorrect = Object.values(subjectData.difficulty || {}).reduce((sum: number, data: any) => sum + (data.correct || 0), 0);
	const totalWrong = Object.values(subjectData.difficulty || {}).reduce((sum: number, data: any) => sum + (data.wrong || 0), 0);
	const totalAttempted = totalCorrect + totalWrong;

	const totalAttemptedData = [
		{ name: "Correct Answers", value: totalCorrect, fill: CHART_COLORS.correct },
		{ name: "Wrong Answers", value: totalWrong, fill: CHART_COLORS.wrong },
	];

	return (
		<Dialog open={isOpen} onOpenChange={onClose}>
			<DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
				<DialogHeader>
					<DialogTitle className="text-xl font-bold text-gray-700">
						{subjectName} - Detailed Analytics
					</DialogTitle>
				</DialogHeader>

				<AnalyticsCard
					title="TOTAL ATTEMPTED MCQ'S"
					value={totalAttempted}
					subtitle="total"
					className="mb-0"
				>
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-9 lg:justify-between">
						<PieChart
							data={totalAttemptedData}
							showLegend={true}
							innerRadius={0}
							outerRadius={70}
							className="flex flex-col justify-between"
						/>
						<AnalyticsSection title="MCQ's Type" data={typeData} borderColor="border-[#D2D5DA]" />
						<AnalyticsSection title="Difficulty Type" data={difficultyData} />
					</div>
				</AnalyticsCard>
			</DialogContent>
		</Dialog>
	);
};

const Page = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// State for modal and time frame selection
	const [selectedSubject, setSelectedSubject] = useState<string | null>(null);
	const [selectedTimeFrame, setSelectedTimeFrame] = useState<TimeFrame>('twentyFourHours');
	const [selectedTest, setSelectedTest] = useState<any | null>(null);

	// Fetch analytics data from API
	const {
		data: analyticsData,
		isLoading,
		error: analyticsError,
	} = useGetAnalytics();
	const analytics = analyticsData?.data?.data?.analytics;

	// Get current time frame data
	const getCurrentTimeFrameData = (): TimeFrameData | undefined => {
		const timeFrameMap: Record<TimeFrame, any> = {
			twentyFourHours: analytics?.twentyFourHours,
			sevenDays: analytics?.sevenDays,
			thirtyDays: analytics?.thirtyDays,
			overall: analytics?.overall,
		};
		return timeFrameMap[selectedTimeFrame];
	};

	const currentData = getCurrentTimeFrameData();

	// Calculate analytics data based on API data
	const totalMCQsSolved = currentData?.mcqsSolvedCount || 0;
	const correctAnswers = currentData?.correctAnswers || 0;
	const wrongAnswers = currentData?.wrongAnswers || 0;

	// Determine if we should show empty state
	const hasAnalyticsData = !isLoading && !analyticsError && totalMCQsSolved > 0;

	// Get quiz name and AI analytics
	const quizName = analytics?.quiz_name || "NUST-Engineering";
	const aiAnalytic = analytics?.ai_based_analytic || "You need to give some more quizzes in order to get helpful AI insights";
	const aiTopicAnalytics = analytics?.ai_topic_analytic || {};

	// Dummy subject feedback data
	const dummySubjectFeedback = [
		{
			subject: "Maths",
			aiRecommendation: "Your foundations are strong, you just need more practice in Trigonometry and Calculus.",
			weakTopics: ["Trigonometric Functions"],
			aiPrediction: "After covering weak topics, you will be able to achieve 85% accuracy in next test!"
		},
		{
			subject: "Physics",
			aiRecommendation: "Your foundations are strong, you just need more practice in Thermodynamics and Projectile Motion.",
			weakTopics: ["Thermodynamics"],
			aiPrediction: "After covering weak topics, you will be able to achieve 85% accuracy in next test!"
		},
		{
			subject: "Chemistry",
			aiRecommendation: "Your foundations are strong, you just need more practice in Inorganic Chemistry and Benzenes.",
			weakTopics: ["Benzenes"],
			aiPrediction: "After covering weak topics, you will be able to achieve 85% accuracy in next test!"
		},
		{
			subject: "English",
			aiRecommendation: "Your foundations are strong, you just need more practice in Subject-Verb Agreement.",
			weakTopics: ["Subject-Verb Agreement"],
			aiPrediction: "After covering weak topics, you will be able to achieve 85% accuracy in next test!"
		}
	];

	// Dummy test data (since we don't have real data yet)
	const dummyTests = [
		{
			id: 1,
			testName: "NET Engineering Standard",
			date: "07/06/25 18:59",
			totalMarks: 200,
			obtainedMarks: 167,
			mcqsSolved: 15,
			correctAnswers: 12,
			wrongAnswers: 3,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 4, theoretical: 7, cramming: 4 },
			difficultyType: { easy: 8, medium: 4, hard: 3 }
		},
		{
			id: 2,
			testName: "NET Engineering Standard",
			date: "06/06/25 14:30",
			totalMarks: 200,
			obtainedMarks: 145,
			mcqsSolved: 15,
			correctAnswers: 10,
			wrongAnswers: 5,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 5, theoretical: 6, cramming: 4 },
			difficultyType: { easy: 6, medium: 5, hard: 4 }
		},
		{
			id: 3,
			testName: "NET Engineering Standard",
			date: "05/06/25 16:45",
			totalMarks: 200,
			obtainedMarks: 178,
			mcqsSolved: 15,
			correctAnswers: 13,
			wrongAnswers: 2,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 6, theoretical: 5, cramming: 4 },
			difficultyType: { easy: 9, medium: 4, hard: 2 }
		},
		{
			id: 4,
			testName: "NET Engineering Standard",
			date: "04/06/25 11:20",
			totalMarks: 200,
			obtainedMarks: 134,
			mcqsSolved: 15,
			correctAnswers: 9,
			wrongAnswers: 6,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 3, theoretical: 8, cramming: 4 },
			difficultyType: { easy: 5, medium: 6, hard: 4 }
		},
		{
			id: 5,
			testName: "NET Engineering Standard",
			date: "03/06/25 09:15",
			totalMarks: 200,
			obtainedMarks: 156,
			mcqsSolved: 15,
			correctAnswers: 11,
			wrongAnswers: 4,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 4, theoretical: 7, cramming: 4 },
			difficultyType: { easy: 7, medium: 5, hard: 3 }
		},
		{
			id: 6,
			testName: "NET Engineering Standard",
			date: "02/06/25 13:40",
			totalMarks: 200,
			obtainedMarks: 189,
			mcqsSolved: 15,
			correctAnswers: 14,
			wrongAnswers: 1,
			subjects: { Mathematics: 5, Physics: 5, English: 5 },
			mcqsType: { numerical: 5, theoretical: 6, cramming: 4 },
			difficultyType: { easy: 10, medium: 3, hard: 2 }
		}
	];

	// Analytics data using real API data where possible
	const totalAttemptedData = [
		{
			name: "Correct Answers",
			value: correctAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: wrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	// Transform analytics data using utility functions
	const mcqTypeData = transformDataToAnalytics(
		currentData?.mcqsType || {},
		currentData?.subject_division || {},
		'type'
	);

	const difficultyTypeData = transformDataToAnalytics(
		currentData?.difficultyType || {},
		currentData?.subject_division || {},
		'difficulty'
	);

	const subjectWiseData = Object.entries(currentData?.subjects || {}).map(([subject, value]) => {
		const subjectData = currentData?.subject_division?.[subject];
		const subjectCorrect = Object.values(subjectData?.difficulty || {}).reduce((sum: number, data: any) => sum + (data.correct || 0), 0);
		const subjectWrong = Object.values(subjectData?.difficulty || {}).reduce((sum: number, data: any) => sum + (data.wrong || 0), 0);

		return {
			name: subject,
			total: value as number,
			correct: { value: subjectCorrect, fill: CHART_COLORS.correct },
			wrong: { value: subjectWrong, fill: CHART_COLORS.wrong },
		};
	});

	// Subject-wise data for progress bars
	const _subjectWiseData = Object.entries(currentData?.subjects || {}).map(([subject, value]) => ({
		name: subject,
		totalValue: 100,
		lightValue: totalMCQsSolved > 0 ? ((value as number) * 100) / totalMCQsSolved : 0,
	}));

	const isFuturePredictionComingSoon = true;

	// Future predictions with slight improvement over current performance
	const predictedCorrectAnswers = Math.round(correctAnswers * 1.1); // 10% improvement prediction
	const predictedWrongAnswers = Math.round(wrongAnswers * 0.9); // 10% reduction in wrong answers

	const futurePredictionData = [
		{
			name: "Correct Answers",
			value: predictedCorrectAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: predictedWrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	const subjectWisePredictionData = _subjectWiseData.map((item) => ({
		name: item.name,
		lightValue: Math.min(Math.round(item.lightValue * 1.1), 95), // 10% improvement, capped at 95%
		totalValue: 100,
	}));

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}
			<main className="container mx-auto p-4 pb-20 lg:pb-4">
				{/* I'm preparing for section */}
				<div className="mb-7 flex items-center gap-3">
					<span className="text-gray-600">I'm preparing for:</span>
					<div className="bg-accent text-white px-4 py-2 rounded-full text-sm font-medium flex items-center gap-2">
						<span>{quizName}</span>
					</div>
					<Button variant="ghost" className="text-accent text-sm">
						Change
					</Button>
				</div>

				{/* Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Analytics
					</h2>
				</div>

				{/* AI Based Analytics Section */}
				{hasAnalyticsData && (
					<div className="mb-9 px-6 py-7 bg-gradient-to-r from-purple-50 to-blue-50 rounded-3xl border border-purple-200">
						<div className="flex items-start gap-3 mb-6">
							<div className="flex-shrink-0">
								<Star className="w-6 h-6 text-accent" />
							</div>
							<div className="flex-1">
								<h3 className="text-lg font-bold text-gray-800 mb-3 flex items-center gap-2">
									<Star className="w-5 h-5 text-accent" />
									AI Based Analytics
								</h3>
								<p className="text-gray-700 leading-relaxed mb-4">
									{aiAnalytic}
								</p>

								{/* Subject-wise AI Topic Analytics */}
								{Object.keys(aiTopicAnalytics).length > 0 && (
									<div className="space-y-3">
										<h4 className="text-md font-semibold text-gray-800 mb-3">
											Subject-wise AI Insights:
										</h4>
										<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
											{Object.entries(aiTopicAnalytics).map(([subject, insight]) => (
												<div
													key={subject}
													className="bg-white/70 backdrop-blur-sm rounded-lg p-4 border border-purple-100"
												>
													<h5 className="font-semibold text-gray-800 mb-2 flex items-center gap-2">
														<div className="w-2 h-2 bg-accent rounded-full"></div>
														{subject}
													</h5>
													<p className="text-sm text-gray-600 leading-relaxed">
														{insight}
													</p>
												</div>
											))}
										</div>
									</div>
								)}
							</div>
						</div>
					</div>
				)}

				{/* Overview Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Overview
					</h2>
				</div>

				{/* Analytics with Time Frame Tabs */}
				{isLoading ? (
					<AnalyticsMainLoading />
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" />
				) : (
					<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
						{/* Time Frame Tabs */}
						<div className="mb-6 flex gap-2">
							{TIME_FRAME_OPTIONS.map(({ key, label }) => (
								<Button
									key={key}
									onClick={() => setSelectedTimeFrame(key)}
									className={`px-4 py-2 rounded-full text-sm font-medium ${
										selectedTimeFrame === key
											? 'bg-accent text-white'
											: 'bg-gray-100 text-gray-600 hover:bg-gray-200'
									}`}
								>
									{label}
								</Button>
							))}
						</div>

						{/* Header */}
						<div className="mb-6">
							<h3 className="text-sm font-bold text-gray-400 mb-2.5">Report</h3>
							<p className="text-[32px] font-medium text-gray-700">
								{totalMCQsSolved} <span className="text-base font-medium text-gray-400">total</span>
							</p>
							<button className="text-accent text-sm mt-2 underline hover:no-underline transition-all duration-200">
								Show Total
							</button>
						</div>

						{/* Main Content Grid */}
						<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
							<PieChart
								data={totalAttemptedData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="flex flex-col justify-between"
							/>
							<AnalyticsSection
								title="MCQ's Type"
								data={mcqTypeData}
								borderColor="border-[#D2D5DA]"
							/>
							<AnalyticsSection
								title="Difficulty Type"
								data={difficultyTypeData}
							/>
							<AnalyticsSection
								title="Subject Wise"
								data={subjectWiseData}
								onItemClick={(item) => setSelectedSubject(item.name)}
							/>
						</div>
					</div>
				)}

				{/* Tests Taken Section */}
				{!selectedTest ? (
					<div className="mb-9">
						<div className="mb-7">
							<h2 className="font-bold text-2xl text-[#202224]">
								Tests Taken
							</h2>
						</div>

						<div className="bg-white rounded-3xl border border-gray-300 overflow-hidden">
							{/* Table Header */}
							<div className="grid grid-cols-6 gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-sm font-semibold text-gray-600">
								<div>Sr</div>
								<div>Test Name</div>
								<div>Date</div>
								<div>Total Marks</div>
								<div>Obtained Marks</div>
								<div></div>
							</div>

							{/* Table Rows */}
							<div className="divide-y divide-gray-100">
								{dummyTests.map((test, index) => (
									<div
										key={test.id}
										className="grid grid-cols-6 gap-4 px-6 py-4 hover:bg-gray-50 transition-colors duration-200"
									>
										<div className="text-sm text-gray-600">
											{String(index + 1).padStart(2, '0')}
										</div>
										<div>
											<button
												onClick={() => setSelectedTest(test)}
												className="text-accent hover:underline text-sm font-medium"
											>
												{test.testName}
											</button>
										</div>
										<div className="text-sm text-gray-600">
											{test.date}
										</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.totalMarks}
										</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.obtainedMarks}
										</div>
										<div>
											<button
												onClick={() => setSelectedTest(test)}
												className="text-accent hover:underline text-sm"
											>
												Analytics
											</button>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				) : (
					/* Test Detail View */
					<div className="mb-9">
						<div className="mb-7">
							<h2 className="font-bold text-2xl text-[#202224]">
								Tests Taken
							</h2>
						</div>

						<div className="px-6 py-7 bg-white rounded-3xl border border-gray-300">
							{/* Header with Back Button */}
							<div className="mb-6 flex items-center justify-between">
								<div className="flex items-center gap-4">
									<button
										onClick={() => setSelectedTest(null)}
										className="flex items-center gap-2 text-gray-600 hover:text-gray-800 transition-colors"
									>
										<svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
										</svg>
									</button>
									<div>
										<h3 className="text-sm font-bold text-gray-400 mb-2.5">
											Report
										</h3>
										<p className="text-[32px] font-medium text-gray-700">
											{selectedTest.obtainedMarks}{" "}
											<span className="text-base font-medium text-gray-400">
												total
											</span>
										</p>
										<button className="text-accent text-sm mt-2 underline hover:no-underline transition-all duration-200">
											Show Total
										</button>
									</div>
								</div>
								<button className="text-accent text-sm underline hover:no-underline transition-all duration-200">
									Go to test
								</button>
							</div>

							{/* Main Content Grid */}
							<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between">
								<PieChart
									data={[
										{ name: "Correct Answers", value: selectedTest.correctAnswers, fill: CHART_COLORS.correct },
										{ name: "Wrong Answers", value: selectedTest.wrongAnswers, fill: CHART_COLORS.wrong },
									]}
									showLegend={true}
									innerRadius={0}
									outerRadius={70}
									className="flex flex-col justify-between"
								/>
								<TestDetailSection
									title="Subject Wise"
									data={selectedTest.subjects}
									showTotal={true}
								/>
								<TestDetailSection
									title="MCQ's Type"
									data={selectedTest.mcqsType}
									borderColor="border-[#D2D5DA]"
								/>
								<TestDetailSection
									title="Difficulty Type"
									data={selectedTest.difficultyType}
								/>
							</div>
						</div>
					</div>
				)}

				{/* Subject Wise Feedback Section */}
				<div className="mb-9">
					<div className="mb-7">
						<h2 className="font-bold text-2xl text-[#202224]">
							Subject Wise Feedback
						</h2>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						{dummySubjectFeedback.map((feedback, index) => (
							<div
								key={index}
								className="bg-white rounded-3xl border border-gray-300 p-6"
							>
								{/* Subject Header */}
								<div className="flex items-center gap-3 mb-4">
									<div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
										<div className="w-6 h-6 bg-gray-300 rounded-full"></div>
									</div>
									<h3 className="font-semibold text-lg text-gray-800">
										{feedback.subject}
									</h3>
								</div>

								{/* AI-Based Recommendation */}
								<div className="mb-4">
									<h4 className="text-sm font-semibold text-gray-600 mb-2">
										AI-Based Recommendation:
									</h4>
									<p className="text-sm text-accent leading-relaxed">
										"{feedback.aiRecommendation}"
									</p>
								</div>

								{/* Weak Topics */}
								<div className="mb-4">
									<h4 className="text-sm font-semibold text-gray-600 mb-2">
										Weak Topics:
									</h4>
									<ul className="space-y-1">
										{feedback.weakTopics.map((topic, topicIndex) => (
											<li key={topicIndex} className="text-sm text-gray-700 flex items-start gap-2">
												<span className="text-gray-400 mt-1">•</span>
												<span>{topic}</span>
											</li>
										))}
									</ul>
								</div>

								{/* AI-Based Prediction */}
								<div>
									<h4 className="text-sm font-semibold text-gray-600 mb-2">
										AI-Based Prediction:
									</h4>
									<p className="text-sm text-accent leading-relaxed">
										"{feedback.aiPrediction}"
									</p>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Future Predictions Heading (Always Visible) */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Future Predictions
					</h2>
				</div>

				{/* Future Predictions */}
				{isFuturePredictionComingSoon ? (
					<FuturePredictionComingSoon />
				) : isLoading ? (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsFutureLoading type="left" />
						<AnalyticsFutureLoading type="right" />
					</div>
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" layout="grid" />
				) : (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsCard
							title="PREDICTED MCQ'S"
							value={predictedCorrectAnswers + predictedWrongAnswers}
							subtitle="predicted"
							className="flex flex-col"
						>
							<PieChart
								data={futurePredictionData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="h-full flex flex-col justify-between"
							/>
						</AnalyticsCard>

						<AnalyticsCard
							title="SUBJECT WISE PREDICTION"
							value={`${Math.round((correctAnswers / (correctAnswers + wrongAnswers)) * 110) || 75}%`}
							subtitle="predicted avg"
						>
							<div className="space-y-4">
								{subjectWisePredictionData.map((item, index) => (
									<div key={index} className="space-y-2">
										<div className="flex justify-between items-center">
											<span className="text-sm font-medium text-[#1A1C1E]">
												{formatName(item.name)}
											</span>
										</div>
										<div className="flex gap-0.5">
											<div
												className="h-4 bg-[#5936CD80] transition-all duration-300"
												style={{ width: `${item.lightValue}%` }}
											/>
											<div
												className="h-4 bg-accent transition-all duration-300"
												style={{ width: `${item.totalValue - item.lightValue}%` }}
											/>
										</div>
									</div>
								))}
							</div>
						</AnalyticsCard>
					</div>
				)}

				{/* Subject Detail Modal */}
				{selectedSubject && currentData?.subject_division?.[selectedSubject] && (
					<SubjectDetailModal
						isOpen={!!selectedSubject}
						onClose={() => setSelectedSubject(null)}
						subjectName={selectedSubject}
						subjectData={currentData.subject_division[selectedSubject]}
					/>
				)}
			</main>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/analytics")({
	beforeLoad: () => {},
	component: Page,
});
