import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Bookmark, ExternalLink } from "react-feather";

type MobileTestOptionsProps = {
	liveCheckEnabled: boolean;
	setLiveCheckEnabled: (enabled: boolean) => void;
	showResults?: boolean;
};

const MobileTestOptions = ({
	liveCheckEnabled,
	setLiveCheckEnabled,
	showResults = false,
}: MobileTestOptionsProps) => {
	return (
		<div className="space-y-3 py-2">
			<div className="flex justify-between items-center py-2">
				<span className={`font-medium ${showResults ? "text-gray-400" : "text-gray-700"}`}>
					Live Check
				</span>
				<Switch
					checked={liveCheckEnabled}
					onCheckedChange={setLiveCheckEnabled}
					disabled={showResults}
				/>
			</div>

			<div className="flex justify-between items-center py-2">
				<span className="text-gray-700 font-medium">Save Test</span>
				<Button
					variant="ghost"
					size="icon"
					className="text-gray-500 h-8 w-8 p-0"
				>
					<Bookmark className="h-4 w-4" />
				</Button>
			</div>

			<div className="flex justify-between items-center py-2">
				<span className="text-gray-700 font-medium">See Test Info</span>
				<Button
					variant="ghost"
					size="icon"
					className="text-gray-500 h-8 w-8 p-0"
				>
					<ExternalLink className="h-4 w-4" />
				</Button>
			</div>
		</div>
	);
};

export default MobileTestOptions;
