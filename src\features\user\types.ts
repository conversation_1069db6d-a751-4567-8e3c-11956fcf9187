import { APIResponse } from "@/lib/api/types";

type Analytics = {
	mcqsSolvedCount: number;
	totalQuizzesTaken: number;
	avgTimePerQuiz: number;
	avgScorePerQuiz: number;
};

export type TimeFrameData = {
	mcqsSolvedCount: number;
	avgTimePerQuiz: number;
	avgScorePerQuiz: number;
	totalQuizzesTaken: number;
	totalMcqs: number;
	totalTime: number;
	correctAnswers: number;
	wrongAnswers: number;
	mcqsType: {
		[key: string]: number;
	};
	difficultyType: {
		[key: string]: number;
	};
	subjects: {
		[key: string]: number;
	};
	subject_division: {
		[key: string]: {
			difficulty: {
				[key: string]: {
					correct: number;
					wrong: number;
				};
			};
			type: {
				[key: string]: {
					correct: number;
					wrong: number;
				};
			};
			topics: {
				[key: string]: {
					correct: number;
					wrong: number;
				};
			};
		};
	};
};

export type AnalyticsData = {
	_id: string;
	user_id: string;
	quiz_name: string;
	ai_based_analytic: string;
	ai_topic_analytic: {
		[key: string]: string;
	};
	lastUpdated: string;
	overall: TimeFrameData;
	sevenDays: TimeFrameData;
	thirtyDays: TimeFrameData;
	twentyFourHours: TimeFrameData;
};

export type DetailedAnalytics = {
	analytics: AnalyticsData;
};

export type User = {
	_id: string;
	firebase_id: string;
	name: string;
	email: string;
	phoneNumber: string;
	city: string;
	institute: string;
	role: string;
	educationBackground: string;
	currentClass: string;
	subjectGroup: any[];
	targetEntryTests: any[];
	analytics: Analytics;
	quizzes: any[];
};

export type ProfilePictureUploadUrl = {
	url: string;
	method: string;
	expiresIn: number;
};

export type ProfilePictureUrl = {
	url: string;
	expiresIn: number;
};

export type GetUserRes = APIResponse<User>;
export type OnboardUserRes = APIResponse<User>;
export type UpdateUserRes = APIResponse<User>;
export type ProfilePictureUploadUrlRes = APIResponse<ProfilePictureUploadUrl>;
export type ProfilePictureUrlRes = APIResponse<ProfilePictureUrl>;
export type GetAnalyticsRes = APIResponse<DetailedAnalytics>;
