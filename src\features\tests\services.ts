import { api } from "@/lib/api";
import { APIResponse } from "@/lib/api/types";
import { AxiosResponse } from "axios";
import { SubmitQuizPayload, SubmitQuizResponse } from "./types";

export interface MockTestRequest {
	entryTest: string;
}

export interface MockTestMCQ {
	_id: string;
	subject: string;
	type: string;
	title: string;
	topic: string;
	difficulty: "easy" | "medium" | "hard";
	repitition: boolean;
	resource?: string;
	explanation: string;
	entryTest: string[];
	options: string[];
	answer: number;
	chapter: number;
}

export interface MockTestResponse {
	quizId: string;
	mcqsBySubject: {
		[subject: string]: MockTestMCQ[];
	};
	count: number;
	subjectCounts: {
		[subject: string]: number;
	};
	time: {
		minutes: number;
		seconds: number;
	};
	requestedParams: {
		entryTest: string;
		weightages: {
			[subject: string]: number;
		};
	};
}

export interface MockTest {
	testId: string;
	university: string;
	name: string;
	description: string;
}

export interface GetMockTestsResponse {
	mockTests: MockTest[];
	count: number;
}

export const generateMockTest = async (
	request: MockTestRequest
): Promise<AxiosResponse<APIResponse<MockTestResponse>>> => {
	return await api.post<APIResponse<MockTestResponse>>(
		"/quiz/generate-mock",
		request
	);
};

export const submitQuiz = async (
	request: SubmitQuizPayload
): Promise<AxiosResponse<APIResponse<SubmitQuizResponse>>> => {
	return await api.post<APIResponse<SubmitQuizResponse>>(
		"/quiz/submit-quiz",
		request
	);
};

export const getMockTests = () => {
	return api.get<APIResponse<GetMockTestsResponse>>("/quiz/mock-tests");
};
